## LLM参数
llm_conf:
  ## LLM url
  llm_url: "http://xxx"

  ## LLM key
  llm_api_key: "EMPTY"

  ## LLM模型
  # llm_model: "Qwen2.5-7B" # ollama模型: "qwen2.5:72b"
  llm_model: "qwen3_32b" 

  ## 是否使用ollama本地部署模型
  use_ollama: False

  ## 是否使用vllm本地部署模型
  use_vllm: True

  ## 在线调用LLM最大尝试次数
  max_error: 3

  ## 使用ollama部署模型使用的GPU数量
  gpu_nums: 1

## 任务参数
task_conf:
  ## 生成图谱的层数
  level_num: 2

  ## 头实体匹配多进程数量（-1表示使用所有CPU核心）
  num_processes_match: 16

  ## 推理多进程数量（-1表示使用所有CPU核心）
  num_processes_infer: 16

  ## 是否跳过三元组抽取
  skip_extract_triple: False

  ## 是否为triple抽取描述
  extract_desc: True

  ## 头实体路径
  # 115机器上Dbpedia英文实体清单：/cpfs04/shared/ADLab/datasets/H_RAG/dbpedia_entities_clean_valid.txt
  # 115机器上Wiki中文实体清单：/data/H-RAG/data/triple_data_by_pl/triple_data/wiki_CN_entity_52w_0326.txt
  pedia_entity_path: CommonKG/config/dbpedia_entities_clean_valid.txt # data/wtr_entity_test.txt
  # pedia_entity_path: data/wtr_entity_test.txt # data/wtr_entity_test.txt

  ## 待检索语料路径（可以是单一文件或者处理后的文件夹）
  # corpus_path: /data/H-RAG/processed_wtr_reports_jsons_0318/wtr03_e_by_page_block.jsonl
  # 示例data：data/wtr03_e_by_page_block-head_20.jsonl or data/wtr03_e_by_page_block-head_100.jsonl
  corpus_path: datasets/mix/mix_chunk.json

  ## 输出结果目录
  output_dir: ckg_data

  ## 参考的开源三元组文件路径
  ref_kg_path: CommonKG/config/triple_ref_test.txt
