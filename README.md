# 🏛️ Aplikasi AI Konsultan Pajak

Aplikasi web fullstack untuk konsultasi pajak menggunakan AI dengan teknologi **LeanRAG** untuk processing PDF yang efisien dan **n8n** untuk workflow automation.

## ✨ Fitur Utama

### 🤖 **AI Chat Interface**
- Interface modern mirip ChatGPT dengan tema pajak Indonesia
- Real-time messaging dengan typing indicators
- Chat history dengan kategorisasi (SPT, Bukti Potong, Umum)
- Suggestions dan quick actions

### 📄 **PDF Management System**
- Upload dan preview PDF dokumen pajak
- Auto-download template formulir pajak
- Processing PDF dengan LeanRAG untuk semantic search
- Bulk download dan management

### 🧠 **Advanced RAG System (LeanRAG)**
- Semantic chunking untuk dokumen pajak
- Knowledge graph construction
- Hierarchical retrieval dengan contextual aggregation
- 46% lebih efisien dibanding flat retrieval

### 🔄 **n8n Integration**
- Workflow automation untuk AI processing
- Webhook endpoints untuk real-time integration
- Automated document processing pipeline

## 🏗️ Arsitektur Sistem

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React + Vite  │────│   Express.js    │────│   LeanRAG       │
│   + Tailwind 4  │    │   Backend       │    │   System        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Socket.IO     │    │   SQLite/       │    │   n8n           │
│   Real-time     │    │   PostgreSQL    │    │   Workflows     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- Python 3.10+
- Git

### 1. Clone Repository
```bash
git clone <repository-url>
cd aplikasi-cek-pajak
```

### 2. Install Dependencies
```bash
# Install semua dependencies
npm run setup

# Atau manual:
npm install
cd frontend && npm install
cd ../backend && npm install
cd ../LeanRAG && pip install -r requirements.txt
```

### 3. Environment Setup
```bash
# Copy environment files
cp backend/.env.example backend/.env
# Edit backend/.env dengan konfigurasi Anda
```

### 4. Run Development
```bash
# Jalankan frontend dan backend bersamaan
npm run dev

# Atau jalankan terpisah:
npm run dev:frontend  # Port 3000
npm run dev:backend   # Port 5000
```

## 📁 Struktur Project

```
aplikasi-cek-pajak/
├── frontend/                   # React + Vite + TypeScript
│   ├── src/
│   │   ├── components/         # Chat UI, PDF Viewer, Upload
│   │   ├── pages/             # Dashboard, Chat
│   │   ├── services/          # API calls
│   │   ├── hooks/             # Custom React hooks
│   │   └── utils/             # Helper functions
├── backend/                    # Express.js + TypeScript
│   ├── src/
│   │   ├── routes/            # API endpoints
│   │   ├── services/          # Business logic
│   │   ├── models/            # Database models
│   │   ├── middleware/        # Auth, CORS, Rate limiting
│   │   ├── rag/               # LeanRAG integration
│   │   └── utils/             # PDF processing, logging
├── LeanRAG/                    # Advanced RAG Framework
│   ├── CommonKG/              # Knowledge Graph extraction
│   ├── GraphExtraction/       # Graph-based extraction
│   ├── datasets/              # Sample datasets
│   └── tools/                 # Utility tools
├── shared/                     # Shared types & constants
└── docs/                      # Documentation
```

## 🛠️ Tech Stack

### Frontend
- **React 18** + **TypeScript**
- **Vite** (build tool)
- **Tailwind CSS 4** (styling)
- **Zustand** (state management)
- **React Query** (data fetching)
- **React PDF** (PDF viewer)
- **Framer Motion** (animations)
- **Socket.IO Client** (real-time)

### Backend
- **Express.js** + **TypeScript**
- **Socket.IO** (real-time communication)
- **Multer** (file upload)
- **PDF-parse** (text extraction)
- **Prisma** (ORM)
- **SQLite/PostgreSQL** (database)
- **Rate limiting** & **Security middleware**

### AI & Processing
- **LeanRAG** (Advanced RAG system)
- **OpenAI Embeddings** (vector embeddings)
- **Chroma/Qdrant** (vector database)
- **n8n** (workflow automation)

## 📚 API Documentation

### Chat Endpoints
- `GET /api/chat/sessions` - Get chat sessions
- `POST /api/chat/sessions` - Create new session
- `POST /api/chat/sessions/:id/messages` - Send message
- `GET /api/chat/suggestions` - Get suggestions

### Upload Endpoints
- `POST /api/upload` - Upload files
- `GET /api/upload/files` - Get uploaded files
- `GET /api/upload/files/:id/download` - Download file
- `POST /api/upload/files/:id/process` - Process with LeanRAG

### PDF Endpoints
- `GET /api/pdf/templates` - Get tax templates
- `POST /api/pdf/templates/:id/download` - Download template
- `GET /api/pdf/auto-download` - Get recommendations
- `POST /api/pdf/bulk-download` - Bulk download

### RAG Endpoints
- `POST /api/rag/query` - Query RAG system
- `GET /api/rag/status` - Get RAG status
- `POST /api/rag/process-document` - Process document
- `GET /api/rag/categories` - Get categories

### n8n Endpoints
- `POST /api/n8n/webhook/tax-ai` - Webhook endpoint
- `GET /api/n8n/executions` - Get executions
- `POST /api/n8n/trigger` - Trigger workflow
- `GET /api/n8n/status` - Get n8n status

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
# Server
PORT=5000
NODE_ENV=development

# Database
DATABASE_URL="file:./dev.db"

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d

# OpenAI
OPENAI_API_KEY=your-openai-key

# LeanRAG
LEANRAG_MODEL=qwen2.5
LEANRAG_BASE_URL=http://localhost:8001/v1
LEANRAG_API_KEY=ollama
LEANRAG_EMBEDDING_MODEL=bge_m3

# n8n
N8N_WEBHOOK_URL=http://localhost:5678/webhook/tax-ai
N8N_API_KEY=your-n8n-key

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads
ALLOWED_FILE_TYPES=pdf,doc,docx

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
CORS_ORIGIN=http://localhost:3000
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test
npm test -- --grep "chat"
```

## 📦 Deployment

### Production Build
```bash
npm run build
```

### Docker (Coming Soon)
```bash
docker-compose up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [LeanRAG](https://github.com/RaZzzyz/LeanRAG) - Advanced RAG framework
- [n8n](https://n8n.io/) - Workflow automation
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [React](https://reactjs.org/) - UI library

## 📞 Support

Jika Anda memiliki pertanyaan atau membutuhkan bantuan:

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our server](https://discord.gg/example)
- 📖 Documentation: [docs.example.com](https://docs.example.com)

---

**Made with ❤️ for Indonesian Tax Consultants**
