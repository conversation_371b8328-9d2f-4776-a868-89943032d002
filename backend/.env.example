# Server Configuration
PORT=5000
NODE_ENV=development

# Database
DATABASE_URL="file:./dev.db"

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# OpenAI API (untuk embeddings)
OPENAI_API_KEY=your-openai-api-key-here

# LeanRAG Configuration
LEANRAG_MODEL=qwen2.5
LEANRAG_BASE_URL=http://localhost:8001/v1
LEANRAG_API_KEY=ollama
LEANRAG_EMBEDDING_MODEL=bge_m3
LEANRAG_EMBEDDING_DIM=1024

# n8n Integration
N8N_WEBHOOK_URL=http://localhost:5678/webhook/tax-ai
N8N_API_KEY=your-n8n-api-key

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads
ALLOWED_FILE_TYPES=pdf,doc,docx

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log
