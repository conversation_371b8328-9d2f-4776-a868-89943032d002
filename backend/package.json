{"name": "aplikasi-cek-pajak-backend", "version": "1.0.0", "description": "Backend API untuk Aplikasi Chat AI Konsultasi Pajak dengan LeanRAG", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["pajak", "tax", "ai", "rag", "express", "typescript"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.5", "dotenv": "^16.3.1", "axios": "^1.6.2", "pdf-parse": "^1.1.1", "sqlite3": "^5.1.6", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "compression": "^1.7.4", "rate-limiter-flexible": "^3.0.8"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/compression": "^1.7.5", "@types/pdf-parse": "^1.1.4", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}}