import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import { createServer } from 'http'
import { Server } from 'socket.io'
import dotenv from 'dotenv'
import path from 'path'

// Import routes
import chatRoutes from './routes/chat'
import pdfRoutes from './routes/pdf'
import uploadRoutes from './routes/upload'
import ragRoutes from './routes/rag'
import n8nRoutes from './routes/n8n'

// Import middleware
import { errorHandler } from './middleware/errorHandler'
import { rateLimiter } from './middleware/rateLimiter'
import { logger } from './utils/logger'

// Load environment variables
dotenv.config()

const app = express()
const server = createServer(app)
const io = new Server(server, {
  cors: {
    origin: process.env.CORS_ORIGIN || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
})

const PORT = process.env.PORT || 5000

// Middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}))
app.use(cors({
  origin: process.env.CORS_ORIGIN || "http://localhost:3000",
  credentials: true
}))
app.use(compression())
app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Rate limiting
app.use(rateLimiter)

// Static files (untuk uploaded files)
app.use('/uploads', express.static(path.join(__dirname, '../uploads')))

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV
  })
})

// API Routes
app.use('/api/chat', chatRoutes)
app.use('/api/pdf', pdfRoutes)
app.use('/api/upload', uploadRoutes)
app.use('/api/rag', ragRoutes)
app.use('/api/n8n', n8nRoutes)

// Socket.IO for real-time chat
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`)

  socket.on('join-room', (roomId: string) => {
    socket.join(roomId)
    logger.info(`Client ${socket.id} joined room: ${roomId}`)
  })

  socket.on('send-message', async (data) => {
    try {
      // Process message with LeanRAG
      const { message, roomId } = data
      
      // Emit typing indicator
      socket.to(roomId).emit('typing', { userId: socket.id, isTyping: true })
      
      // Here we'll integrate with LeanRAG for processing
      // For now, just echo back
      setTimeout(() => {
        socket.to(roomId).emit('typing', { userId: socket.id, isTyping: false })
        socket.to(roomId).emit('message', {
          id: Date.now().toString(),
          type: 'ai',
          content: `AI Response to: ${message}`,
          timestamp: new Date()
        })
      }, 2000)
      
    } catch (error) {
      logger.error('Error processing message:', error)
      socket.emit('error', { message: 'Failed to process message' })
    }
  })

  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`)
  })
})

// Error handling
app.use(errorHandler)

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: 'Route not found',
    path: req.originalUrl,
    method: req.method
  })
})

// Start server
server.listen(PORT, () => {
  logger.info(`🚀 Server running on port ${PORT}`)
  logger.info(`📱 Environment: ${process.env.NODE_ENV}`)
  logger.info(`🔗 CORS Origin: ${process.env.CORS_ORIGIN}`)
  
  // Initialize LeanRAG
  initializeLeanRAG()
})

async function initializeLeanRAG() {
  try {
    logger.info('🤖 Initializing LeanRAG system...')
    // LeanRAG initialization will be implemented here
    logger.info('✅ LeanRAG system initialized successfully')
  } catch (error) {
    logger.error('❌ Failed to initialize LeanRAG:', error)
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully')
  server.close(() => {
    logger.info('Process terminated')
    process.exit(0)
  })
})

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully')
  server.close(() => {
    logger.info('Process terminated')
    process.exit(0)
  })
})

export default app
