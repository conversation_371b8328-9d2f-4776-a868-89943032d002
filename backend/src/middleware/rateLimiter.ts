import { Request, Response, NextFunction } from 'express'
import { RateLimiterMemory } from 'rate-limiter-flexible'
import { logger } from '../utils/logger'

// Rate limiter configuration
const rateLimiter = new RateLimiterMemory({
  keyGenerator: (req: Request) => req.ip, // Use IP as key
  points: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // Number of requests
  duration: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000') / 1000, // Per 15 minutes (in seconds)
  blockDuration: 60, // Block for 60 seconds if limit exceeded
})

// Specific rate limiter for file uploads (more restrictive)
const uploadRateLimiter = new RateLimiterMemory({
  keyGenerator: (req: Request) => req.ip,
  points: 10, // 10 uploads
  duration: 3600, // Per hour
  blockDuration: 300, // Block for 5 minutes
})

// Specific rate limiter for chat messages
const chatRateLimiter = new RateLimiterMemory({
  keyGenerator: (req: Request) => req.ip,
  points: 50, // 50 messages
  duration: 900, // Per 15 minutes
  blockDuration: 60, // Block for 1 minute
})

export const rateLimiterMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    await rateLimiter.consume(req.ip)
    next()
  } catch (rejRes: any) {
    const remainingPoints = rejRes?.remainingPoints || 0
    const msBeforeNext = rejRes?.msBeforeNext || 0
    const totalHits = rejRes?.totalHits || 0

    logger.warn(`Rate limit exceeded for IP: ${req.ip}`, {
      path: req.path,
      method: req.method,
      remainingPoints,
      msBeforeNext,
      totalHits
    })

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many requests, please try again later',
        statusCode: 429,
        retryAfter: Math.round(msBeforeNext / 1000) || 60
      },
      timestamp: new Date().toISOString()
    })
  }
}

export const uploadRateLimiterMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    await uploadRateLimiter.consume(req.ip)
    next()
  } catch (rejRes: any) {
    const msBeforeNext = rejRes?.msBeforeNext || 0

    logger.warn(`Upload rate limit exceeded for IP: ${req.ip}`, {
      path: req.path,
      method: req.method
    })

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many file uploads, please try again later',
        statusCode: 429,
        retryAfter: Math.round(msBeforeNext / 1000) || 300
      },
      timestamp: new Date().toISOString()
    })
  }
}

export const chatRateLimiterMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    await chatRateLimiter.consume(req.ip)
    next()
  } catch (rejRes: any) {
    const msBeforeNext = rejRes?.msBeforeNext || 0

    logger.warn(`Chat rate limit exceeded for IP: ${req.ip}`, {
      path: req.path,
      method: req.method
    })

    res.status(429).json({
      success: false,
      error: {
        message: 'Too many chat messages, please slow down',
        statusCode: 429,
        retryAfter: Math.round(msBeforeNext / 1000) || 60
      },
      timestamp: new Date().toISOString()
    })
  }
}

// Export the default rate limiter
export { rateLimiterMiddleware as rateLimiter }
