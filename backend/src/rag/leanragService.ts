import { spawn, ChildProcess } from 'child_process'
import path from 'path'
import fs from 'fs/promises'
import { logger } from '../utils/logger'

export interface LeanRAGConfig {
  model: string
  baseUrl: string
  apiKey: string
  embeddingModel: string
  embeddingDim: number
  maxTokenSize: number
}

export interface DocumentChunk {
  id: string
  text: string
  metadata: {
    source: string
    page?: number
    category: 'spt' | 'bukti-potong' | 'general' | 'regulation'
    timestamp: Date
  }
  embedding?: number[]
}

export interface QueryResult {
  chunks: DocumentChunk[]
  answer: string
  confidence: number
  sources: string[]
}

class LeanRAGService {
  private config: LeanRAGConfig
  private leanragPath: string
  private isInitialized: boolean = false

  constructor() {
    this.config = {
      model: process.env.LEANRAG_MODEL || 'qwen2.5',
      baseUrl: process.env.LEANRAG_BASE_URL || 'http://localhost:8001/v1',
      apiKey: process.env.LEANRAG_API_KEY || 'ollama',
      embeddingModel: process.env.LEANRAG_EMBEDDING_MODEL || 'bge_m3',
      embeddingDim: parseInt(process.env.LEANRAG_EMBEDDING_DIM || '1024'),
      maxTokenSize: parseInt(process.env.LEANRAG_MAX_TOKEN_SIZE || '8192')
    }
    
    // Path to LeanRAG directory
    this.leanragPath = path.join(__dirname, '../../../LeanRAG')
  }

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing LeanRAG service...')
      
      // Check if LeanRAG directory exists
      await fs.access(this.leanragPath)
      
      // Update LeanRAG config
      await this.updateLeanRAGConfig()
      
      // Initialize vector database if needed
      await this.initializeVectorDB()
      
      this.isInitialized = true
      logger.info('LeanRAG service initialized successfully')
    } catch (error) {
      logger.error('Failed to initialize LeanRAG service:', error)
      throw error
    }
  }

  private async updateLeanRAGConfig(): Promise<void> {
    const configPath = path.join(this.leanragPath, 'config.yaml')
    const configContent = `
glm:
  model: "${this.config.embeddingModel}"
  base_url: "${this.config.baseUrl}"
  embedding_model: "${this.config.embeddingModel}"

deepseek:
  model: "${this.config.model}"
  api_key: "${this.config.apiKey}"
  base_url: "${this.config.baseUrl}"

model_params:
  openai_embedding_dim: 1536
  glm_embedding_dim: ${this.config.embeddingDim}
  max_token_size: ${this.config.maxTokenSize}
`
    
    await fs.writeFile(configPath, configContent, 'utf-8')
    logger.info('LeanRAG config updated')
  }

  private async initializeVectorDB(): Promise<void> {
    // Create necessary directories
    const dirs = ['output', 'chunks', 'graphs']
    for (const dir of dirs) {
      const dirPath = path.join(this.leanragPath, dir)
      try {
        await fs.access(dirPath)
      } catch {
        await fs.mkdir(dirPath, { recursive: true })
        logger.info(`Created directory: ${dirPath}`)
      }
    }
  }

  async processDocument(filePath: string, category: string): Promise<string> {
    if (!this.isInitialized) {
      throw new Error('LeanRAG service not initialized')
    }

    try {
      logger.info(`Processing document: ${filePath}`)
      
      // Step 1: Chunk the document
      const chunkId = await this.chunkDocument(filePath)
      
      // Step 2: Extract knowledge graph
      await this.extractKnowledgeGraph(chunkId, category)
      
      // Step 3: Build graph
      await this.buildGraph(chunkId)
      
      logger.info(`Document processed successfully: ${chunkId}`)
      return chunkId
    } catch (error) {
      logger.error('Error processing document:', error)
      throw error
    }
  }

  private async chunkDocument(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const chunkId = `doc_${Date.now()}`
      const pythonProcess = spawn('python', [
        path.join(this.leanragPath, 'file_chunk.py'),
        '--input', filePath,
        '--output', path.join(this.leanragPath, 'chunks', `${chunkId}.jsonl`),
        '--chunk-size', '1024',
        '--step', '128'
      ], {
        cwd: this.leanragPath
      })

      let output = ''
      let error = ''

      pythonProcess.stdout.on('data', (data) => {
        output += data.toString()
      })

      pythonProcess.stderr.on('data', (data) => {
        error += data.toString()
      })

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          logger.info(`Document chunked successfully: ${chunkId}`)
          resolve(chunkId)
        } else {
          logger.error(`Chunking failed with code ${code}: ${error}`)
          reject(new Error(`Chunking failed: ${error}`))
        }
      })
    })
  }

  private async extractKnowledgeGraph(chunkId: string, category: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const pythonProcess = spawn('python', [
        path.join(this.leanragPath, 'GraphExtraction', 'chunk.py'),
        '--chunk-file', path.join(this.leanragPath, 'chunks', `${chunkId}.jsonl`),
        '--output-dir', path.join(this.leanragPath, 'output', chunkId),
        '--category', category
      ], {
        cwd: this.leanragPath
      })

      let output = ''
      let error = ''

      pythonProcess.stdout.on('data', (data) => {
        output += data.toString()
      })

      pythonProcess.stderr.on('data', (data) => {
        error += data.toString()
      })

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          logger.info(`Knowledge graph extracted for: ${chunkId}`)
          resolve()
        } else {
          logger.error(`KG extraction failed with code ${code}: ${error}`)
          reject(new Error(`KG extraction failed: ${error}`))
        }
      })
    })
  }

  private async buildGraph(chunkId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const pythonProcess = spawn('python', [
        path.join(this.leanragPath, 'build_graph.py'),
        '--input-dir', path.join(this.leanragPath, 'output', chunkId),
        '--output-dir', path.join(this.leanragPath, 'graphs', chunkId)
      ], {
        cwd: this.leanragPath
      })

      let output = ''
      let error = ''

      pythonProcess.stdout.on('data', (data) => {
        output += data.toString()
      })

      pythonProcess.stderr.on('data', (data) => {
        error += data.toString()
      })

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          logger.info(`Graph built successfully for: ${chunkId}`)
          resolve()
        } else {
          logger.error(`Graph building failed with code ${code}: ${error}`)
          reject(new Error(`Graph building failed: ${error}`))
        }
      })
    })
  }

  async query(question: string, category?: string): Promise<QueryResult> {
    if (!this.isInitialized) {
      throw new Error('LeanRAG service not initialized')
    }

    try {
      logger.info(`Processing query: ${question}`)
      
      return new Promise((resolve, reject) => {
        const pythonProcess = spawn('python', [
          path.join(this.leanragPath, 'query_graph.py'),
          '--query', question,
          '--top-k', '5',
          ...(category ? ['--category', category] : [])
        ], {
          cwd: this.leanragPath
        })

        let output = ''
        let error = ''

        pythonProcess.stdout.on('data', (data) => {
          output += data.toString()
        })

        pythonProcess.stderr.on('data', (data) => {
          error += data.toString()
        })

        pythonProcess.on('close', (code) => {
          if (code === 0) {
            try {
              const result = JSON.parse(output)
              logger.info(`Query processed successfully`)
              resolve(result)
            } catch (parseError) {
              logger.error('Failed to parse query result:', parseError)
              reject(new Error('Failed to parse query result'))
            }
          } else {
            logger.error(`Query failed with code ${code}: ${error}`)
            reject(new Error(`Query failed: ${error}`))
          }
        })
      })
    } catch (error) {
      logger.error('Error processing query:', error)
      throw error
    }
  }

  async getDocumentCategories(): Promise<string[]> {
    try {
      const graphsDir = path.join(this.leanragPath, 'graphs')
      const categories = await fs.readdir(graphsDir)
      return categories.filter(cat => cat !== '.DS_Store')
    } catch (error) {
      logger.error('Error getting document categories:', error)
      return []
    }
  }

  async deleteDocument(chunkId: string): Promise<void> {
    try {
      const paths = [
        path.join(this.leanragPath, 'chunks', `${chunkId}.jsonl`),
        path.join(this.leanragPath, 'output', chunkId),
        path.join(this.leanragPath, 'graphs', chunkId)
      ]

      for (const p of paths) {
        try {
          await fs.rm(p, { recursive: true, force: true })
        } catch (error) {
          logger.warn(`Failed to delete ${p}:`, error)
        }
      }

      logger.info(`Document deleted: ${chunkId}`)
    } catch (error) {
      logger.error('Error deleting document:', error)
      throw error
    }
  }
}

export const leanragService = new LeanRAGService()
