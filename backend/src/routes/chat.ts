import { Router } from 'express'
import { asyncHand<PERSON> } from '../middleware/errorHandler'
import { chatRateLimiterMiddleware } from '../middleware/rateLimiter'
import { leanragService } from '../rag/leanragService'
import { logger } from '../utils/logger'

const router = Router()

// Interface untuk chat message
interface ChatMessage {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  metadata?: {
    category?: string
    confidence?: number
    sources?: string[]
  }
}

// Interface untuk chat session
interface ChatSession {
  id: string
  title: string
  messages: ChatMessage[]
  category: string
  createdAt: Date
  updatedAt: Date
}

// Mock storage - dalam implementasi nyata akan menggunakan database
const chatSessions: Map<string, ChatSession> = new Map()

// GET /api/chat/sessions - Get all chat sessions
router.get('/sessions', asyncHandler(async (req, res) => {
  const sessions = Array.from(chatSessions.values())
    .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
  
  res.json({
    success: true,
    data: sessions.map(session => ({
      id: session.id,
      title: session.title,
      category: session.category,
      messageCount: session.messages.length,
      lastMessage: session.messages[session.messages.length - 1]?.content.substring(0, 100),
      createdAt: session.createdAt,
      updatedAt: session.updatedAt
    }))
  })
}))

// GET /api/chat/sessions/:id - Get specific chat session
router.get('/sessions/:id', asyncHandler(async (req, res) => {
  const { id } = req.params
  const session = chatSessions.get(id)
  
  if (!session) {
    return res.status(404).json({
      success: false,
      error: { message: 'Chat session not found', statusCode: 404 }
    })
  }
  
  res.json({
    success: true,
    data: session
  })
}))

// POST /api/chat/sessions - Create new chat session
router.post('/sessions', asyncHandler(async (req, res) => {
  const { title, category = 'general' } = req.body
  
  const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  const newSession: ChatSession = {
    id: sessionId,
    title: title || 'Chat Baru',
    messages: [],
    category,
    createdAt: new Date(),
    updatedAt: new Date()
  }
  
  chatSessions.set(sessionId, newSession)
  
  logger.info(`New chat session created: ${sessionId}`)
  
  res.status(201).json({
    success: true,
    data: newSession
  })
}))

// POST /api/chat/sessions/:id/messages - Send message to chat session
router.post('/sessions/:id/messages', 
  chatRateLimiterMiddleware,
  asyncHandler(async (req, res) => {
    const { id } = req.params
    const { content, category } = req.body
    
    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: { message: 'Message content is required', statusCode: 400 }
      })
    }
    
    const session = chatSessions.get(id)
    if (!session) {
      return res.status(404).json({
        success: false,
        error: { message: 'Chat session not found', statusCode: 404 }
      })
    }
    
    // Add user message
    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}_user`,
      type: 'user',
      content: content.trim(),
      timestamp: new Date()
    }
    
    session.messages.push(userMessage)
    session.updatedAt = new Date()
    
    try {
      // Process with LeanRAG
      logger.info(`Processing message with LeanRAG: ${content.substring(0, 50)}...`)
      
      const ragResult = await leanragService.query(content, category || session.category)
      
      // Create AI response
      const aiMessage: ChatMessage = {
        id: `msg_${Date.now()}_ai`,
        type: 'ai',
        content: ragResult.answer,
        timestamp: new Date(),
        metadata: {
          category: category || session.category,
          confidence: ragResult.confidence,
          sources: ragResult.sources
        }
      }
      
      session.messages.push(aiMessage)
      session.updatedAt = new Date()
      
      // Update session title if it's the first message
      if (session.messages.length === 2 && session.title === 'Chat Baru') {
        session.title = content.substring(0, 50) + (content.length > 50 ? '...' : '')
      }
      
      logger.info(`Message processed successfully for session: ${id}`)
      
      res.json({
        success: true,
        data: {
          userMessage,
          aiMessage,
          session: {
            id: session.id,
            title: session.title,
            messageCount: session.messages.length,
            updatedAt: session.updatedAt
          }
        }
      })
      
    } catch (error) {
      logger.error('Error processing message with LeanRAG:', error)
      
      // Fallback response
      const aiMessage: ChatMessage = {
        id: `msg_${Date.now()}_ai`,
        type: 'ai',
        content: 'Maaf, saya mengalami kesulitan memproses pertanyaan Anda saat ini. Silakan coba lagi dalam beberapa saat atau hubungi administrator jika masalah berlanjut.',
        timestamp: new Date(),
        metadata: {
          category: 'error',
          confidence: 0
        }
      }
      
      session.messages.push(aiMessage)
      session.updatedAt = new Date()
      
      res.json({
        success: true,
        data: {
          userMessage,
          aiMessage,
          session: {
            id: session.id,
            title: session.title,
            messageCount: session.messages.length,
            updatedAt: session.updatedAt
          }
        },
        warning: 'AI service temporarily unavailable, fallback response provided'
      })
    }
  })
)

// DELETE /api/chat/sessions/:id - Delete chat session
router.delete('/sessions/:id', asyncHandler(async (req, res) => {
  const { id } = req.params
  
  if (!chatSessions.has(id)) {
    return res.status(404).json({
      success: false,
      error: { message: 'Chat session not found', statusCode: 404 }
    })
  }
  
  chatSessions.delete(id)
  logger.info(`Chat session deleted: ${id}`)
  
  res.json({
    success: true,
    message: 'Chat session deleted successfully'
  })
}))

// GET /api/chat/suggestions - Get chat suggestions based on category
router.get('/suggestions', asyncHandler(async (req, res) => {
  const { category = 'general' } = req.query
  
  const suggestions = {
    spt: [
      'Bagaimana cara mengisi SPT Tahunan Pribadi?',
      'Apa saja dokumen yang diperlukan untuk SPT?',
      'Kapan batas waktu pelaporan SPT Tahunan?',
      'Bagaimana cara menghitung pajak terutang?'
    ],
    'bukti-potong': [
      'Apa itu bukti potong PPh 21?',
      'Bagaimana cara mendapatkan bukti potong dari perusahaan?',
      'Apa fungsi bukti potong dalam pelaporan pajak?',
      'Bagaimana jika bukti potong hilang?'
    ],
    general: [
      'Apa saja jenis pajak di Indonesia?',
      'Bagaimana cara mendaftar NPWP?',
      'Apa itu PKP dan bagaimana cara mendaftarnya?',
      'Bagaimana menghitung pajak untuk freelancer?'
    ]
  }
  
  res.json({
    success: true,
    data: {
      category,
      suggestions: suggestions[category as keyof typeof suggestions] || suggestions.general
    }
  })
}))

export default router
