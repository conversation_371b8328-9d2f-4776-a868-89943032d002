import { Router } from 'express'
import { as<PERSON><PERSON>and<PERSON> } from '../middleware/errorHandler'
import { logger } from '../utils/logger'
import axios from 'axios'

const router = Router()

// Interface untuk n8n workflow
interface N8NWorkflow {
  id: string
  name: string
  active: boolean
  webhookUrl?: string
}

// Interface untuk n8n execution
interface N8NExecution {
  id: string
  workflowId: string
  status: 'running' | 'success' | 'error' | 'waiting'
  startedAt: Date
  finishedAt?: Date
  data?: any
}

// Mock storage untuk executions
const executions: Map<string, N8NExecution> = new Map()

// POST /api/n8n/webhook/tax-ai - Webhook endpoint untuk n8n
router.post('/webhook/tax-ai', asyncHandler(async (req, res) => {
  const { query, category, sessionId, userId } = req.body
  
  logger.info('Received n8n webhook request:', { query, category, sessionId, userId })
  
  try {
    // Create execution record
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const execution: N8NExecution = {
      id: executionId,
      workflowId: 'tax-ai-workflow',
      status: 'running',
      startedAt: new Date(),
      data: { query, category, sessionId, userId }
    }
    
    executions.set(executionId, execution)
    
    // Simulate processing (dalam implementasi nyata akan memanggil LeanRAG)
    setTimeout(() => {
      const updatedExecution = executions.get(executionId)
      if (updatedExecution) {
        updatedExecution.status = 'success'
        updatedExecution.finishedAt = new Date()
        updatedExecution.data = {
          ...updatedExecution.data,
          response: `AI response untuk: ${query}`,
          confidence: 0.85,
          sources: ['document1.pdf', 'regulation2.pdf']
        }
        executions.set(executionId, updatedExecution)
      }
    }, 2000)
    
    res.json({
      success: true,
      data: {
        executionId,
        status: 'accepted',
        message: 'Request received and processing started'
      }
    })
    
  } catch (error) {
    logger.error('Error processing n8n webhook:', error)
    res.status(500).json({
      success: false,
      error: { message: 'Failed to process webhook', statusCode: 500 }
    })
  }
}))

// GET /api/n8n/executions/:id - Get execution status
router.get('/executions/:id', asyncHandler(async (req, res) => {
  const { id } = req.params
  const execution = executions.get(id)
  
  if (!execution) {
    return res.status(404).json({
      success: false,
      error: { message: 'Execution not found', statusCode: 404 }
    })
  }
  
  res.json({
    success: true,
    data: execution
  })
}))

// GET /api/n8n/executions - Get all executions
router.get('/executions', asyncHandler(async (req, res) => {
  const { status, workflowId, limit = 50 } = req.query
  
  let filteredExecutions = Array.from(executions.values())
  
  if (status) {
    filteredExecutions = filteredExecutions.filter(exec => exec.status === status)
  }
  
  if (workflowId) {
    filteredExecutions = filteredExecutions.filter(exec => exec.workflowId === workflowId)
  }
  
  // Sort by start time (newest first)
  filteredExecutions.sort((a, b) => b.startedAt.getTime() - a.startedAt.getTime())
  
  // Apply limit
  const limitNum = parseInt(limit as string)
  if (limitNum > 0) {
    filteredExecutions = filteredExecutions.slice(0, limitNum)
  }
  
  res.json({
    success: true,
    data: {
      executions: filteredExecutions,
      total: filteredExecutions.length
    }
  })
}))

// POST /api/n8n/trigger - Trigger n8n workflow
router.post('/trigger', asyncHandler(async (req, res) => {
  const { workflowName, data } = req.body
  
  if (!workflowName) {
    return res.status(400).json({
      success: false,
      error: { message: 'Workflow name is required', statusCode: 400 }
    })
  }
  
  try {
    const n8nWebhookUrl = process.env.N8N_WEBHOOK_URL
    const n8nApiKey = process.env.N8N_API_KEY
    
    if (!n8nWebhookUrl) {
      return res.status(500).json({
        success: false,
        error: { message: 'n8n webhook URL not configured', statusCode: 500 }
      })
    }
    
    logger.info(`Triggering n8n workflow: ${workflowName}`)
    
    const response = await axios.post(n8nWebhookUrl, {
      workflowName,
      data,
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Content-Type': 'application/json',
        ...(n8nApiKey && { 'Authorization': `Bearer ${n8nApiKey}` })
      },
      timeout: 10000
    })
    
    res.json({
      success: true,
      data: {
        workflowName,
        triggered: true,
        response: response.data,
        timestamp: new Date()
      },
      message: 'Workflow triggered successfully'
    })
    
  } catch (error) {
    logger.error('Error triggering n8n workflow:', error)
    
    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        return res.status(408).json({
          success: false,
          error: { message: 'n8n request timeout', statusCode: 408 }
        })
      } else if (error.response?.status) {
        return res.status(error.response.status).json({
          success: false,
          error: { 
            message: `n8n error: ${error.response.statusText}`, 
            statusCode: error.response.status 
          }
        })
      }
    }
    
    res.status(500).json({
      success: false,
      error: { message: 'Failed to trigger workflow', statusCode: 500 }
    })
  }
}))

// GET /api/n8n/workflows - Get available workflows
router.get('/workflows', asyncHandler(async (req, res) => {
  // Mock workflows - dalam implementasi nyata akan mengambil dari n8n API
  const workflows: N8NWorkflow[] = [
    {
      id: 'tax-ai-workflow',
      name: 'Tax AI Processing',
      active: true,
      webhookUrl: `${process.env.N8N_WEBHOOK_URL}/tax-ai`
    },
    {
      id: 'pdf-processing-workflow',
      name: 'PDF Document Processing',
      active: true,
      webhookUrl: `${process.env.N8N_WEBHOOK_URL}/pdf-process`
    },
    {
      id: 'notification-workflow',
      name: 'User Notifications',
      active: true,
      webhookUrl: `${process.env.N8N_WEBHOOK_URL}/notifications`
    }
  ]
  
  res.json({
    success: true,
    data: {
      workflows,
      total: workflows.length,
      active: workflows.filter(w => w.active).length
    }
  })
}))

// POST /api/n8n/workflows/:id/activate - Activate/deactivate workflow
router.post('/workflows/:id/activate', asyncHandler(async (req, res) => {
  const { id } = req.params
  const { active } = req.body
  
  if (typeof active !== 'boolean') {
    return res.status(400).json({
      success: false,
      error: { message: 'Active status (boolean) is required', statusCode: 400 }
    })
  }
  
  try {
    // Dalam implementasi nyata akan memanggil n8n API
    logger.info(`${active ? 'Activating' : 'Deactivating'} workflow: ${id}`)
    
    res.json({
      success: true,
      data: {
        workflowId: id,
        active,
        updatedAt: new Date()
      },
      message: `Workflow ${active ? 'activated' : 'deactivated'} successfully`
    })
    
  } catch (error) {
    logger.error('Error updating workflow status:', error)
    res.status(500).json({
      success: false,
      error: { message: 'Failed to update workflow status', statusCode: 500 }
    })
  }
}))

// GET /api/n8n/status - Get n8n integration status
router.get('/status', asyncHandler(async (req, res) => {
  try {
    const n8nWebhookUrl = process.env.N8N_WEBHOOK_URL
    const n8nApiKey = process.env.N8N_API_KEY
    
    let n8nStatus = 'unknown'
    let n8nVersion = null
    
    if (n8nWebhookUrl) {
      try {
        // Test connection to n8n (dalam implementasi nyata)
        n8nStatus = 'connected'
        n8nVersion = '1.0.0' // Mock version
      } catch {
        n8nStatus = 'disconnected'
      }
    } else {
      n8nStatus = 'not_configured'
    }
    
    res.json({
      success: true,
      data: {
        status: n8nStatus,
        configured: !!n8nWebhookUrl,
        authenticated: !!n8nApiKey,
        webhookUrl: n8nWebhookUrl,
        version: n8nVersion,
        executions: {
          total: executions.size,
          running: Array.from(executions.values()).filter(e => e.status === 'running').length,
          success: Array.from(executions.values()).filter(e => e.status === 'success').length,
          error: Array.from(executions.values()).filter(e => e.status === 'error').length
        },
        timestamp: new Date()
      }
    })
    
  } catch (error) {
    logger.error('Error getting n8n status:', error)
    res.status(500).json({
      success: false,
      error: { message: 'Failed to get n8n status', statusCode: 500 }
    })
  }
}))

// DELETE /api/n8n/executions/:id - Delete execution record
router.delete('/executions/:id', asyncHandler(async (req, res) => {
  const { id } = req.params
  
  if (!executions.has(id)) {
    return res.status(404).json({
      success: false,
      error: { message: 'Execution not found', statusCode: 404 }
    })
  }
  
  executions.delete(id)
  
  res.json({
    success: true,
    message: 'Execution deleted successfully'
  })
}))

export default router
