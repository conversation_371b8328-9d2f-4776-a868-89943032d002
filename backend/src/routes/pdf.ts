import { Router } from 'express'
import { asyncHandler } from '../middleware/errorHandler'
import { logger } from '../utils/logger'
import axios from 'axios'
import fs from 'fs/promises'
import path from 'path'

const router = Router()

// Interface untuk template pajak
interface TaxTemplate {
  id: string
  name: string
  category: 'spt' | 'bukti-potong' | 'formulir' | 'panduan'
  description: string
  year: number
  url: string
  size?: number
  lastUpdated: Date
}

// Mock data template pajak - dalam implementasi nyata akan dari database/API pajak.go.id
const taxTemplates: TaxTemplate[] = [
  {
    id: 'spt-1770s-2024',
    name: 'SPT Tahunan PPh Orang Pribadi (1770S)',
    category: 'spt',
    description: 'Formulir SPT Tahunan PPh Orang Pribadi untuk tahun pajak 2024',
    year: 2024,
    url: 'https://www.pajak.go.id/sites/default/files/2024-02/1770S%202024.pdf',
    lastUpdated: new Date('2024-02-01')
  },
  {
    id: 'spt-1770-2024',
    name: 'SPT Tahunan PPh Orang Pribadi (1770)',
    category: 'spt',
    description: 'Formulir SPT Tahunan PPh Orang Pribadi lengkap untuk tahun pajak 2024',
    year: 2024,
    url: 'https://www.pajak.go.id/sites/default/files/2024-02/1770%202024.pdf',
    lastUpdated: new Date('2024-02-01')
  },
  {
    id: 'bukpot-pph21-2024',
    name: 'Bukti Pemotongan PPh Pasal 21',
    category: 'bukti-potong',
    description: 'Formulir bukti pemotongan PPh Pasal 21 untuk karyawan',
    year: 2024,
    url: 'https://www.pajak.go.id/sites/default/files/2024-01/Bukti%20Potong%20PPh%2021.pdf',
    lastUpdated: new Date('2024-01-15')
  },
  {
    id: 'panduan-spt-2024',
    name: 'Panduan Pengisian SPT Tahunan 2024',
    category: 'panduan',
    description: 'Panduan lengkap pengisian SPT Tahunan PPh Orang Pribadi',
    year: 2024,
    url: 'https://www.pajak.go.id/sites/default/files/2024-02/Panduan%20SPT%202024.pdf',
    lastUpdated: new Date('2024-02-15')
  }
]

// GET /api/pdf/templates - Get all tax templates
router.get('/templates', asyncHandler(async (req, res) => {
  const { category, year } = req.query
  
  let filteredTemplates = [...taxTemplates]
  
  if (category) {
    filteredTemplates = filteredTemplates.filter(template => template.category === category)
  }
  
  if (year) {
    filteredTemplates = filteredTemplates.filter(template => template.year === parseInt(year as string))
  }
  
  // Sort by last updated (newest first)
  filteredTemplates.sort((a, b) => b.lastUpdated.getTime() - a.lastUpdated.getTime())
  
  res.json({
    success: true,
    data: {
      templates: filteredTemplates,
      categories: ['spt', 'bukti-potong', 'formulir', 'panduan'],
      years: [2024, 2023, 2022]
    }
  })
}))

// GET /api/pdf/templates/:id - Get specific template
router.get('/templates/:id', asyncHandler(async (req, res) => {
  const { id } = req.params
  const template = taxTemplates.find(t => t.id === id)
  
  if (!template) {
    return res.status(404).json({
      success: false,
      error: { message: 'Template not found', statusCode: 404 }
    })
  }
  
  res.json({
    success: true,
    data: template
  })
}))

// POST /api/pdf/templates/:id/download - Download template
router.post('/templates/:id/download', asyncHandler(async (req, res) => {
  const { id } = req.params
  const template = taxTemplates.find(t => t.id === id)
  
  if (!template) {
    return res.status(404).json({
      success: false,
      error: { message: 'Template not found', statusCode: 404 }
    })
  }
  
  try {
    logger.info(`Downloading template: ${template.name}`)
    
    // Create downloads directory if it doesn't exist
    const downloadsDir = path.join(process.env.UPLOAD_DIR || './uploads', 'downloads')
    try {
      await fs.access(downloadsDir)
    } catch {
      await fs.mkdir(downloadsDir, { recursive: true })
    }
    
    // Check if file already exists locally
    const localFilePath = path.join(downloadsDir, `${template.id}.pdf`)
    let fileExists = false
    
    try {
      await fs.access(localFilePath)
      fileExists = true
      logger.info(`Template already exists locally: ${localFilePath}`)
    } catch {
      // File doesn't exist, need to download
    }
    
    if (!fileExists) {
      // Download from external URL
      logger.info(`Downloading from: ${template.url}`)
      
      const response = await axios({
        method: 'GET',
        url: template.url,
        responseType: 'stream',
        timeout: 30000, // 30 seconds timeout
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; TaxAI/1.0)'
        }
      })
      
      // Save to local file
      const writer = require('fs').createWriteStream(localFilePath)
      response.data.pipe(writer)
      
      await new Promise((resolve, reject) => {
        writer.on('finish', resolve)
        writer.on('error', reject)
      })
      
      logger.info(`Template downloaded successfully: ${localFilePath}`)
    }
    
    // Get file stats
    const stats = await fs.stat(localFilePath)
    
    res.json({
      success: true,
      data: {
        template,
        localPath: `/uploads/downloads/${template.id}.pdf`,
        size: stats.size,
        downloadedAt: new Date()
      },
      message: 'Template downloaded successfully'
    })
    
  } catch (error) {
    logger.error(`Error downloading template ${id}:`, error)
    
    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        return res.status(408).json({
          success: false,
          error: { message: 'Download timeout', statusCode: 408 }
        })
      } else if (error.response?.status === 404) {
        return res.status(404).json({
          success: false,
          error: { message: 'Template file not found on server', statusCode: 404 }
        })
      }
    }
    
    res.status(500).json({
      success: false,
      error: { message: 'Failed to download template', statusCode: 500 }
    })
  }
}))

// GET /api/pdf/auto-download - Get auto-download recommendations
router.get('/auto-download', asyncHandler(async (req, res) => {
  const { category, userType = 'individual' } = req.query
  
  // Logic untuk rekomendasi auto-download berdasarkan kategori dan tipe user
  let recommendations: TaxTemplate[] = []
  
  if (category === 'spt' || !category) {
    if (userType === 'individual') {
      recommendations.push(
        ...taxTemplates.filter(t => t.id.includes('1770s') || t.id.includes('panduan'))
      )
    } else if (userType === 'business') {
      recommendations.push(
        ...taxTemplates.filter(t => t.id.includes('1770') && !t.id.includes('1770s'))
      )
    }
  }
  
  if (category === 'bukti-potong' || !category) {
    recommendations.push(
      ...taxTemplates.filter(t => t.category === 'bukti-potong')
    )
  }
  
  // Remove duplicates
  recommendations = recommendations.filter((template, index, self) => 
    index === self.findIndex(t => t.id === template.id)
  )
  
  res.json({
    success: true,
    data: {
      recommendations,
      userType,
      category: category || 'all',
      message: `Found ${recommendations.length} recommended templates`
    }
  })
}))

// POST /api/pdf/bulk-download - Bulk download templates
router.post('/bulk-download', asyncHandler(async (req, res) => {
  const { templateIds } = req.body
  
  if (!Array.isArray(templateIds) || templateIds.length === 0) {
    return res.status(400).json({
      success: false,
      error: { message: 'Template IDs array is required', statusCode: 400 }
    })
  }
  
  if (templateIds.length > 10) {
    return res.status(400).json({
      success: false,
      error: { message: 'Maximum 10 templates can be downloaded at once', statusCode: 400 }
    })
  }
  
  const results = []
  
  for (const templateId of templateIds) {
    const template = taxTemplates.find(t => t.id === templateId)
    
    if (!template) {
      results.push({
        templateId,
        success: false,
        error: 'Template not found'
      })
      continue
    }
    
    try {
      // Similar download logic as single download
      const downloadsDir = path.join(process.env.UPLOAD_DIR || './uploads', 'downloads')
      const localFilePath = path.join(downloadsDir, `${template.id}.pdf`)
      
      let fileExists = false
      try {
        await fs.access(localFilePath)
        fileExists = true
      } catch {
        // File doesn't exist, need to download
      }
      
      if (!fileExists) {
        const response = await axios({
          method: 'GET',
          url: template.url,
          responseType: 'stream',
          timeout: 30000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; TaxAI/1.0)'
          }
        })
        
        const writer = require('fs').createWriteStream(localFilePath)
        response.data.pipe(writer)
        
        await new Promise((resolve, reject) => {
          writer.on('finish', resolve)
          writer.on('error', reject)
        })
      }
      
      const stats = await fs.stat(localFilePath)
      
      results.push({
        templateId,
        success: true,
        template,
        localPath: `/uploads/downloads/${template.id}.pdf`,
        size: stats.size
      })
      
    } catch (error) {
      logger.error(`Error downloading template ${templateId}:`, error)
      results.push({
        templateId,
        success: false,
        error: 'Download failed'
      })
    }
  }
  
  const successCount = results.filter(r => r.success).length
  const failCount = results.filter(r => !r.success).length
  
  res.json({
    success: true,
    data: {
      results,
      summary: {
        total: templateIds.length,
        success: successCount,
        failed: failCount
      }
    },
    message: `Bulk download completed: ${successCount} success, ${failCount} failed`
  })
}))

// GET /api/pdf/categories - Get available categories
router.get('/categories', asyncHandler(async (req, res) => {
  const categories = [
    {
      id: 'spt',
      name: 'SPT (Surat Pemberitahuan)',
      description: 'Formulir SPT Tahunan dan SPT Masa',
      count: taxTemplates.filter(t => t.category === 'spt').length
    },
    {
      id: 'bukti-potong',
      name: 'Bukti Potong',
      description: 'Formulir bukti pemotongan pajak',
      count: taxTemplates.filter(t => t.category === 'bukti-potong').length
    },
    {
      id: 'formulir',
      name: 'Formulir Pajak',
      description: 'Berbagai formulir perpajakan',
      count: taxTemplates.filter(t => t.category === 'formulir').length
    },
    {
      id: 'panduan',
      name: 'Panduan & Petunjuk',
      description: 'Panduan pengisian dan petunjuk teknis',
      count: taxTemplates.filter(t => t.category === 'panduan').length
    }
  ]
  
  res.json({
    success: true,
    data: categories
  })
}))

export default router
