import { Router } from 'express'
import { as<PERSON><PERSON>and<PERSON> } from '../middleware/errorHandler'
import { leanragService } from '../rag/leanragService'
import { logger } from '../utils/logger'

const router = Router()

// GET /api/rag/status - Get LeanRAG service status
router.get('/status', asyncHandler(async (req, res) => {
  try {
    const categories = await leanragService.getDocumentCategories()
    
    res.json({
      success: true,
      data: {
        status: 'active',
        initialized: true,
        categories,
        categoryCount: categories.length,
        timestamp: new Date()
      }
    })
  } catch (error) {
    logger.error('Error getting LeanRAG status:', error)
    res.json({
      success: true,
      data: {
        status: 'error',
        initialized: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      }
    })
  }
}))

// POST /api/rag/query - Query the RAG system
router.post('/query', asyncHandler(async (req, res) => {
  const { question, category, topK = 5 } = req.body
  
  if (!question || typeof question !== 'string' || question.trim().length === 0) {
    return res.status(400).json({
      success: false,
      error: { message: 'Question is required', statusCode: 400 }
    })
  }
  
  try {
    logger.info(`RAG query: ${question.substring(0, 100)}...`)
    
    const result = await leanragService.query(question.trim(), category)
    
    res.json({
      success: true,
      data: {
        question,
        answer: result.answer,
        confidence: result.confidence,
        sources: result.sources,
        chunks: result.chunks.map(chunk => ({
          id: chunk.id,
          text: chunk.text.substring(0, 200) + '...', // Truncate for response
          metadata: chunk.metadata
        })),
        metadata: {
          category,
          topK,
          processedAt: new Date()
        }
      }
    })
    
  } catch (error) {
    logger.error('Error processing RAG query:', error)
    res.status(500).json({
      success: false,
      error: { 
        message: 'Failed to process query', 
        statusCode: 500,
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    })
  }
}))

// POST /api/rag/initialize - Initialize or reinitialize LeanRAG
router.post('/initialize', asyncHandler(async (req, res) => {
  try {
    logger.info('Reinitializing LeanRAG service...')
    
    await leanragService.initialize()
    
    res.json({
      success: true,
      data: {
        status: 'initialized',
        timestamp: new Date()
      },
      message: 'LeanRAG service initialized successfully'
    })
    
  } catch (error) {
    logger.error('Error initializing LeanRAG:', error)
    res.status(500).json({
      success: false,
      error: { 
        message: 'Failed to initialize LeanRAG', 
        statusCode: 500,
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    })
  }
}))

// GET /api/rag/categories - Get available document categories
router.get('/categories', asyncHandler(async (req, res) => {
  try {
    const categories = await leanragService.getDocumentCategories()
    
    res.json({
      success: true,
      data: {
        categories,
        count: categories.length
      }
    })
    
  } catch (error) {
    logger.error('Error getting categories:', error)
    res.status(500).json({
      success: false,
      error: { 
        message: 'Failed to get categories', 
        statusCode: 500 
      }
    })
  }
}))

// POST /api/rag/process-document - Process a document with LeanRAG
router.post('/process-document', asyncHandler(async (req, res) => {
  const { filePath, category = 'general' } = req.body
  
  if (!filePath || typeof filePath !== 'string') {
    return res.status(400).json({
      success: false,
      error: { message: 'File path is required', statusCode: 400 }
    })
  }
  
  try {
    logger.info(`Processing document with LeanRAG: ${filePath}`)
    
    const chunkId = await leanragService.processDocument(filePath, category)
    
    res.json({
      success: true,
      data: {
        chunkId,
        filePath,
        category,
        processedAt: new Date()
      },
      message: 'Document processed successfully'
    })
    
  } catch (error) {
    logger.error('Error processing document:', error)
    res.status(500).json({
      success: false,
      error: { 
        message: 'Failed to process document', 
        statusCode: 500,
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    })
  }
}))

// DELETE /api/rag/documents/:chunkId - Delete a processed document
router.delete('/documents/:chunkId', asyncHandler(async (req, res) => {
  const { chunkId } = req.params
  
  if (!chunkId) {
    return res.status(400).json({
      success: false,
      error: { message: 'Chunk ID is required', statusCode: 400 }
    })
  }
  
  try {
    await leanragService.deleteDocument(chunkId)
    
    res.json({
      success: true,
      message: 'Document deleted successfully'
    })
    
  } catch (error) {
    logger.error('Error deleting document:', error)
    res.status(500).json({
      success: false,
      error: { 
        message: 'Failed to delete document', 
        statusCode: 500 
      }
    })
  }
}))

// POST /api/rag/batch-query - Process multiple queries at once
router.post('/batch-query', asyncHandler(async (req, res) => {
  const { queries } = req.body
  
  if (!Array.isArray(queries) || queries.length === 0) {
    return res.status(400).json({
      success: false,
      error: { message: 'Queries array is required', statusCode: 400 }
    })
  }
  
  if (queries.length > 10) {
    return res.status(400).json({
      success: false,
      error: { message: 'Maximum 10 queries allowed per batch', statusCode: 400 }
    })
  }
  
  try {
    const results = []
    
    for (const query of queries) {
      if (!query.question) {
        results.push({
          question: query.question || '',
          success: false,
          error: 'Question is required'
        })
        continue
      }
      
      try {
        const result = await leanragService.query(query.question, query.category)
        results.push({
          question: query.question,
          success: true,
          answer: result.answer,
          confidence: result.confidence,
          sources: result.sources
        })
      } catch (error) {
        results.push({
          question: query.question,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    const successCount = results.filter(r => r.success).length
    const failCount = results.filter(r => !r.success).length
    
    res.json({
      success: true,
      data: {
        results,
        summary: {
          total: queries.length,
          success: successCount,
          failed: failCount
        }
      }
    })
    
  } catch (error) {
    logger.error('Error processing batch queries:', error)
    res.status(500).json({
      success: false,
      error: { 
        message: 'Failed to process batch queries', 
        statusCode: 500 
      }
    })
  }
}))

// GET /api/rag/health - Health check for RAG system
router.get('/health', asyncHandler(async (req, res) => {
  try {
    // Test query to check if system is working
    const testResult = await leanragService.query('test', 'general')
    
    res.json({
      success: true,
      data: {
        status: 'healthy',
        responseTime: Date.now(),
        testQuery: 'passed'
      }
    })
    
  } catch (error) {
    res.status(503).json({
      success: false,
      data: {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      error: { 
        message: 'RAG system health check failed', 
        statusCode: 503 
      }
    })
  }
}))

export default router
