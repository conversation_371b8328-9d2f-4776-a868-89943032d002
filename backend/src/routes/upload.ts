import { Router } from 'express'
import multer from 'multer'
import path from 'path'
import fs from 'fs/promises'
import { async<PERSON><PERSON><PERSON> } from '../middleware/errorHandler'
import { uploadRateLimiterMiddleware } from '../middleware/rateLimiter'
import { leanragService } from '../rag/leanragService'
import { logger } from '../utils/logger'
import pdfParse from 'pdf-parse'

const router = Router()

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = process.env.UPLOAD_DIR || './uploads'
    try {
      await fs.access(uploadDir)
    } catch {
      await fs.mkdir(uploadDir, { recursive: true })
    }
    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    const ext = path.extname(file.originalname)
    cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`)
  }
})

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'pdf,doc,docx').split(',')
  const fileExt = path.extname(file.originalname).toLowerCase().substring(1)
  
  if (allowedTypes.includes(fileExt)) {
    cb(null, true)
  } else {
    cb(new Error(`File type .${fileExt} not allowed. Allowed types: ${allowedTypes.join(', ')}`))
  }
}

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB default
    files: 5 // Maximum 5 files per request
  }
})

// Interface untuk uploaded file info
interface UploadedFile {
  id: string
  originalName: string
  filename: string
  path: string
  size: number
  mimetype: string
  category: string
  uploadedAt: Date
  processedAt?: Date
  status: 'uploaded' | 'processing' | 'processed' | 'error'
  chunkId?: string
  error?: string
}

// Mock storage - dalam implementasi nyata akan menggunakan database
const uploadedFiles: Map<string, UploadedFile> = new Map()

// POST /api/upload - Upload files
router.post('/',
  uploadRateLimiterMiddleware,
  upload.array('files', 5),
  asyncHandler(async (req, res) => {
    const files = req.files as Express.Multer.File[]
    const { category = 'general' } = req.body
    
    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        error: { message: 'No files uploaded', statusCode: 400 }
      })
    }
    
    const uploadedFileInfos: UploadedFile[] = []
    
    for (const file of files) {
      const fileId = `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const fileInfo: UploadedFile = {
        id: fileId,
        originalName: file.originalname,
        filename: file.filename,
        path: file.path,
        size: file.size,
        mimetype: file.mimetype,
        category,
        uploadedAt: new Date(),
        status: 'uploaded'
      }
      
      uploadedFiles.set(fileId, fileInfo)
      uploadedFileInfos.push(fileInfo)
      
      logger.info(`File uploaded: ${file.originalname} (${fileId})`)
    }
    
    res.json({
      success: true,
      data: {
        files: uploadedFileInfos,
        message: `${files.length} file(s) uploaded successfully`
      }
    })
    
    // Process files asynchronously
    processFilesAsync(uploadedFileInfos)
  })
)

// GET /api/upload/files - Get all uploaded files
router.get('/files', asyncHandler(async (req, res) => {
  const { category, status } = req.query
  
  let files = Array.from(uploadedFiles.values())
  
  if (category) {
    files = files.filter(file => file.category === category)
  }
  
  if (status) {
    files = files.filter(file => file.status === status)
  }
  
  files.sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime())
  
  res.json({
    success: true,
    data: files
  })
}))

// GET /api/upload/files/:id - Get specific file info
router.get('/files/:id', asyncHandler(async (req, res) => {
  const { id } = req.params
  const file = uploadedFiles.get(id)
  
  if (!file) {
    return res.status(404).json({
      success: false,
      error: { message: 'File not found', statusCode: 404 }
    })
  }
  
  res.json({
    success: true,
    data: file
  })
}))

// GET /api/upload/files/:id/download - Download file
router.get('/files/:id/download', asyncHandler(async (req, res) => {
  const { id } = req.params
  const file = uploadedFiles.get(id)
  
  if (!file) {
    return res.status(404).json({
      success: false,
      error: { message: 'File not found', statusCode: 404 }
    })
  }
  
  try {
    await fs.access(file.path)
    res.download(file.path, file.originalName)
  } catch (error) {
    logger.error(`File not found on disk: ${file.path}`)
    res.status(404).json({
      success: false,
      error: { message: 'File not found on disk', statusCode: 404 }
    })
  }
}))

// POST /api/upload/files/:id/process - Manually trigger file processing
router.post('/files/:id/process', asyncHandler(async (req, res) => {
  const { id } = req.params
  const file = uploadedFiles.get(id)
  
  if (!file) {
    return res.status(404).json({
      success: false,
      error: { message: 'File not found', statusCode: 404 }
    })
  }
  
  if (file.status === 'processing') {
    return res.status(409).json({
      success: false,
      error: { message: 'File is already being processed', statusCode: 409 }
    })
  }
  
  if (file.status === 'processed') {
    return res.json({
      success: true,
      data: file,
      message: 'File already processed'
    })
  }
  
  // Start processing
  processFileAsync(file)
  
  res.json({
    success: true,
    data: file,
    message: 'File processing started'
  })
}))

// DELETE /api/upload/files/:id - Delete file
router.delete('/files/:id', asyncHandler(async (req, res) => {
  const { id } = req.params
  const file = uploadedFiles.get(id)
  
  if (!file) {
    return res.status(404).json({
      success: false,
      error: { message: 'File not found', statusCode: 404 }
    })
  }
  
  try {
    // Delete file from disk
    await fs.unlink(file.path)
    
    // Delete from LeanRAG if processed
    if (file.chunkId) {
      await leanragService.deleteDocument(file.chunkId)
    }
    
    // Remove from memory
    uploadedFiles.delete(id)
    
    logger.info(`File deleted: ${file.originalName} (${id})`)
    
    res.json({
      success: true,
      message: 'File deleted successfully'
    })
  } catch (error) {
    logger.error(`Error deleting file ${id}:`, error)
    res.status(500).json({
      success: false,
      error: { message: 'Failed to delete file', statusCode: 500 }
    })
  }
}))

// GET /api/upload/files/:id/preview - Get file preview/content
router.get('/files/:id/preview', asyncHandler(async (req, res) => {
  const { id } = req.params
  const file = uploadedFiles.get(id)
  
  if (!file) {
    return res.status(404).json({
      success: false,
      error: { message: 'File not found', statusCode: 404 }
    })
  }
  
  try {
    if (file.mimetype === 'application/pdf') {
      const buffer = await fs.readFile(file.path)
      const pdfData = await pdfParse(buffer)
      
      res.json({
        success: true,
        data: {
          text: pdfData.text.substring(0, 2000), // First 2000 characters
          pages: pdfData.numpages,
          info: pdfData.info
        }
      })
    } else {
      res.json({
        success: false,
        error: { message: 'Preview not available for this file type', statusCode: 400 }
      })
    }
  } catch (error) {
    logger.error(`Error generating preview for file ${id}:`, error)
    res.status(500).json({
      success: false,
      error: { message: 'Failed to generate preview', statusCode: 500 }
    })
  }
}))

// Async function to process multiple files
async function processFilesAsync(files: UploadedFile[]) {
  for (const file of files) {
    await processFileAsync(file)
  }
}

// Async function to process a single file
async function processFileAsync(file: UploadedFile) {
  try {
    logger.info(`Starting processing for file: ${file.originalName}`)
    
    // Update status
    file.status = 'processing'
    uploadedFiles.set(file.id, file)
    
    // Process with LeanRAG
    const chunkId = await leanragService.processDocument(file.path, file.category)
    
    // Update file info
    file.chunkId = chunkId
    file.status = 'processed'
    file.processedAt = new Date()
    uploadedFiles.set(file.id, file)
    
    logger.info(`File processed successfully: ${file.originalName}`)
  } catch (error) {
    logger.error(`Error processing file ${file.originalName}:`, error)
    
    // Update status to error
    file.status = 'error'
    file.error = error instanceof Error ? error.message : 'Unknown error'
    uploadedFiles.set(file.id, file)
  }
}

export default router
