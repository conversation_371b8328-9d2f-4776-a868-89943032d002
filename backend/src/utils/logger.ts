import { createWriteStream, existsSync, mkdirSync } from 'fs'
import path from 'path'

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

class Logger {
  private logLevel: LogLevel
  private logFile?: NodeJS.WritableStream

  constructor() {
    this.logLevel = this.getLogLevel()
    this.initializeLogFile()
  }

  private getLogLevel(): LogLevel {
    const level = process.env.LOG_LEVEL?.toUpperCase() || 'INFO'
    switch (level) {
      case 'ERROR': return LogLevel.ERROR
      case 'WARN': return LogLevel.WARN
      case 'INFO': return LogLevel.INFO
      case 'DEBUG': return LogLevel.DEBUG
      default: return LogLevel.INFO
    }
  }

  private initializeLogFile() {
    if (process.env.LOG_FILE) {
      const logDir = path.dirname(process.env.LOG_FILE)
      if (!existsSync(logDir)) {
        mkdirSync(logDir, { recursive: true })
      }
      this.logFile = createWriteStream(process.env.LOG_FILE, { flags: 'a' })
    }
  }

  private formatMessage(level: string, message: string, meta?: any): string {
    const timestamp = new Date().toISOString()
    const metaStr = meta ? ` ${JSON.stringify(meta)}` : ''
    return `[${timestamp}] ${level}: ${message}${metaStr}`
  }

  private log(level: LogLevel, levelName: string, message: string, meta?: any) {
    if (level <= this.logLevel) {
      const formattedMessage = this.formatMessage(levelName, message, meta)
      
      // Console output with colors
      const colors = {
        ERROR: '\x1b[31m', // Red
        WARN: '\x1b[33m',  // Yellow
        INFO: '\x1b[36m',  // Cyan
        DEBUG: '\x1b[35m'  // Magenta
      }
      const reset = '\x1b[0m'
      
      console.log(`${colors[levelName as keyof typeof colors]}${formattedMessage}${reset}`)
      
      // File output
      if (this.logFile) {
        this.logFile.write(formattedMessage + '\n')
      }
    }
  }

  error(message: string, meta?: any) {
    this.log(LogLevel.ERROR, 'ERROR', message, meta)
  }

  warn(message: string, meta?: any) {
    this.log(LogLevel.WARN, 'WARN', message, meta)
  }

  info(message: string, meta?: any) {
    this.log(LogLevel.INFO, 'INFO', message, meta)
  }

  debug(message: string, meta?: any) {
    this.log(LogLevel.DEBUG, 'DEBUG', message, meta)
  }
}

export const logger = new Logger()
