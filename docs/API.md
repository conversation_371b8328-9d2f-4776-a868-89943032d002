# 📚 API Documentation

Dokumentasi lengkap API endpoints untuk Aplikasi AI Konsultan Pajak.

## 🔗 Base URL
```
Development: http://localhost:5000/api
Production: https://yourdomain.com/api
```

## 🔐 Authentication
Beberapa endpoints memerlukan authentication menggunakan JWT token.

```http
Authorization: Bearer <your-jwt-token>
```

## 📝 Response Format

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Optional success message"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "statusCode": 400,
    "details": "Additional error details"
  },
  "timestamp": "2024-08-30T07:00:00.000Z",
  "path": "/api/endpoint",
  "method": "POST"
}
```

## 💬 Chat Endpoints

### Get Chat Sessions
```http
GET /api/chat/sessions
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "session_123",
      "title": "SPT Tahunan 2024",
      "category": "spt",
      "messageCount": 5,
      "lastMessage": "Bagaimana cara mengisi...",
      "createdAt": "2024-08-30T07:00:00.000Z",
      "updatedAt": "2024-08-30T07:30:00.000Z"
    }
  ]
}
```

### Create Chat Session
```http
POST /api/chat/sessions
```

**Request Body:**
```json
{
  "title": "Chat Baru",
  "category": "general"
}
```

### Send Message
```http
POST /api/chat/sessions/:id/messages
```

**Request Body:**
```json
{
  "content": "Bagaimana cara mengisi SPT Tahunan?",
  "category": "spt"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "userMessage": {
      "id": "msg_123_user",
      "type": "user",
      "content": "Bagaimana cara mengisi SPT Tahunan?",
      "timestamp": "2024-08-30T07:00:00.000Z"
    },
    "aiMessage": {
      "id": "msg_123_ai",
      "type": "ai",
      "content": "Untuk mengisi SPT Tahunan...",
      "timestamp": "2024-08-30T07:00:01.000Z",
      "metadata": {
        "confidence": 0.95,
        "sources": ["spt_guide.pdf", "regulation.pdf"]
      }
    }
  }
}
```

### Get Chat Suggestions
```http
GET /api/chat/suggestions?category=spt
```

## 📄 Upload Endpoints

### Upload Files
```http
POST /api/upload
```

**Request:** Multipart form data
- `files`: File array (max 5 files)
- `category`: Document category (spt, bukti-potong, general)

**Response:**
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "id": "file_123",
        "originalName": "spt_2024.pdf",
        "filename": "files-1693392000000-*********.pdf",
        "size": 1048576,
        "category": "spt",
        "status": "uploaded",
        "uploadedAt": "2024-08-30T07:00:00.000Z"
      }
    ],
    "message": "1 file(s) uploaded successfully"
  }
}
```

### Get Uploaded Files
```http
GET /api/upload/files?category=spt&status=processed
```

### Download File
```http
GET /api/upload/files/:id/download
```

### Process File with LeanRAG
```http
POST /api/upload/files/:id/process
```

## 📋 PDF Template Endpoints

### Get Tax Templates
```http
GET /api/pdf/templates?category=spt&year=2024
```

**Response:**
```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": "spt-1770s-2024",
        "name": "SPT Tahunan PPh Orang Pribadi (1770S)",
        "category": "spt",
        "description": "Formulir SPT Tahunan PPh Orang Pribadi untuk tahun pajak 2024",
        "year": 2024,
        "url": "https://www.pajak.go.id/sites/default/files/2024-02/1770S%202024.pdf",
        "lastUpdated": "2024-02-01T00:00:00.000Z"
      }
    ],
    "categories": ["spt", "bukti-potong", "formulir", "panduan"],
    "years": [2024, 2023, 2022]
  }
}
```

### Download Template
```http
POST /api/pdf/templates/:id/download
```

### Get Auto-Download Recommendations
```http
GET /api/pdf/auto-download?category=spt&userType=individual
```

### Bulk Download Templates
```http
POST /api/pdf/bulk-download
```

**Request Body:**
```json
{
  "templateIds": ["spt-1770s-2024", "bukpot-pph21-2024"]
}
```

## 🧠 RAG System Endpoints

### Query RAG System
```http
POST /api/rag/query
```

**Request Body:**
```json
{
  "question": "Bagaimana cara menghitung pajak penghasilan?",
  "category": "spt",
  "topK": 5
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "question": "Bagaimana cara menghitung pajak penghasilan?",
    "answer": "Pajak penghasilan dihitung berdasarkan...",
    "confidence": 0.92,
    "sources": ["tax_regulation.pdf", "spt_guide.pdf"],
    "chunks": [
      {
        "id": "chunk_123",
        "text": "Pajak penghasilan adalah...",
        "metadata": {
          "source": "tax_regulation.pdf",
          "page": 15,
          "category": "spt"
        }
      }
    ],
    "metadata": {
      "category": "spt",
      "topK": 5,
      "processedAt": "2024-08-30T07:00:00.000Z"
    }
  }
}
```

### Get RAG Status
```http
GET /api/rag/status
```

### Process Document with RAG
```http
POST /api/rag/process-document
```

**Request Body:**
```json
{
  "filePath": "/path/to/document.pdf",
  "category": "spt"
}
```

### Batch Query
```http
POST /api/rag/batch-query
```

**Request Body:**
```json
{
  "queries": [
    {
      "question": "Apa itu SPT?",
      "category": "spt"
    },
    {
      "question": "Bagaimana cara menghitung pajak?",
      "category": "general"
    }
  ]
}
```

## 🔄 n8n Integration Endpoints

### Webhook Endpoint
```http
POST /api/n8n/webhook/tax-ai
```

**Request Body:**
```json
{
  "query": "Bagaimana cara mengisi SPT?",
  "category": "spt",
  "sessionId": "session_123",
  "userId": "user_456"
}
```

### Get Execution Status
```http
GET /api/n8n/executions/:id
```

### Trigger Workflow
```http
POST /api/n8n/trigger
```

**Request Body:**
```json
{
  "workflowName": "tax-ai-workflow",
  "data": {
    "query": "Test query",
    "category": "general"
  }
}
```

### Get n8n Status
```http
GET /api/n8n/status
```

## 📊 Health Check Endpoints

### Application Health
```http
GET /health
```

**Response:**
```json
{
  "status": "OK",
  "timestamp": "2024-08-30T07:00:00.000Z",
  "uptime": 3600,
  "environment": "production"
}
```

### RAG System Health
```http
GET /api/rag/health
```

## 🚫 Rate Limiting

### Default Limits
- **General API**: 100 requests per 15 minutes
- **Chat Messages**: 50 messages per 15 minutes  
- **File Upload**: 10 uploads per hour

### Rate Limit Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

### Rate Limit Exceeded Response
```json
{
  "success": false,
  "error": {
    "message": "Too many requests, please try again later",
    "statusCode": 429,
    "retryAfter": 60
  },
  "timestamp": "2024-08-30T07:00:00.000Z"
}
```

## 🔍 Query Parameters

### Common Parameters
- `limit`: Number of results (default: 50, max: 100)
- `offset`: Pagination offset (default: 0)
- `sort`: Sort field (e.g., `createdAt`, `updatedAt`)
- `order`: Sort order (`asc`, `desc`)
- `category`: Filter by category
- `status`: Filter by status

### Example
```http
GET /api/chat/sessions?limit=20&offset=0&sort=updatedAt&order=desc&category=spt
```

## 🔒 Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input |
| 401 | Unauthorized - Missing/invalid token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 408 | Request Timeout |
| 409 | Conflict - Resource already exists |
| 413 | Payload Too Large - File too big |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error |
| 503 | Service Unavailable |

## 📝 Examples

### Complete Chat Flow
```javascript
// 1. Create session
const session = await fetch('/api/chat/sessions', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    title: 'SPT Consultation',
    category: 'spt'
  })
})

// 2. Send message
const response = await fetch(`/api/chat/sessions/${session.id}/messages`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    content: 'How to fill SPT form?',
    category: 'spt'
  })
})
```

### File Upload and Processing
```javascript
// 1. Upload file
const formData = new FormData()
formData.append('files', file)
formData.append('category', 'spt')

const upload = await fetch('/api/upload', {
  method: 'POST',
  body: formData
})

// 2. Process with RAG
const process = await fetch(`/api/upload/files/${upload.data.files[0].id}/process`, {
  method: 'POST'
})
```

---

**Need help?** Contact our support team or check the [GitHub Issues](https://github.com/your-repo/issues).
