# 🚀 Deployment Guide

Panduan lengkap untuk deploy Aplikasi AI Konsultan Pajak ke production.

## 📋 Prerequisites

### Server Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 20GB SSD
- **CPU**: 2+ cores
- **Network**: Public IP dengan port 80, 443 terbuka

### Software Requirements
- Docker & Docker Compose
- Node.js 18+
- Python 3.10+
- Nginx (untuk reverse proxy)
- SSL Certificate (Let's Encrypt recommended)

## 🐳 Docker Deployment (Recommended)

### 1. Clone Repository
```bash
git clone <repository-url>
cd aplikasi-cek-pajak
```

### 2. Environment Setup
```bash
# Copy environment files
cp backend/.env.example backend/.env

# Edit production configuration
nano backend/.env
```

### 3. Production Environment Variables
```env
# Server
NODE_ENV=production
PORT=5000

# Database
DATABASE_URL=*************************************************/taxai

# Security
JWT_SECRET=your-super-secure-jwt-secret-here

# OpenAI
OPENAI_API_KEY=your-openai-api-key

# LeanRAG
LEANRAG_MODEL=qwen2.5
LEANRAG_BASE_URL=http://ollama:11434/v1
LEANRAG_API_KEY=ollama

# n8n
N8N_WEBHOOK_URL=http://n8n:5678/webhook/tax-ai
N8N_API_KEY=your-n8n-api-key

# CORS
CORS_ORIGIN=https://yourdomain.com
```

### 4. SSL Certificate Setup
```bash
# Create SSL directory
mkdir -p nginx/ssl

# Generate Let's Encrypt certificate
sudo certbot certonly --standalone -d yourdomain.com
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem nginx/ssl/
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem nginx/ssl/
```

### 5. Deploy with Docker Compose
```bash
# Build and start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

### 6. Initialize Database
```bash
# Run database migrations
docker-compose exec backend npx prisma db push

# Seed initial data (optional)
docker-compose exec backend npm run seed
```

## 🔧 Manual Deployment

### 1. Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Python
sudo apt install python3 python3-pip python3-venv -y

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# Install Redis
sudo apt install redis-server -y

# Install Nginx
sudo apt install nginx -y
```

### 2. Application Setup
```bash
# Clone repository
git clone <repository-url>
cd aplikasi-cek-pajak

# Install dependencies
npm run setup

# Build frontend
npm run build

# Setup environment
cp backend/.env.example backend/.env
# Edit backend/.env with production values
```

### 3. Database Setup
```bash
# Create database
sudo -u postgres createdb taxai
sudo -u postgres createuser taxai_user
sudo -u postgres psql -c "ALTER USER taxai_user PASSWORD 'your-password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE taxai TO taxai_user;"

# Run migrations
cd backend
npx prisma db push
```

### 4. Process Manager (PM2)
```bash
# Install PM2
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'tax-ai-backend',
    script: 'dist/index.js',
    cwd: './backend',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    }
  }]
}
EOF

# Start application
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save
pm2 startup
```

### 5. Nginx Configuration
```nginx
# /etc/nginx/sites-available/tax-ai
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/ssl/fullchain.pem;
    ssl_certificate_key /path/to/ssl/privkey.pem;

    # Frontend
    location / {
        root /path/to/aplikasi-cek-pajak/frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Socket.IO
    location /socket.io/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # File uploads
    location /uploads/ {
        alias /path/to/aplikasi-cek-pajak/backend/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/tax-ai /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔒 Security Hardening

### 1. Firewall Setup
```bash
# UFW firewall
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. SSL Security
```bash
# Add to Nginx configuration
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
```

### 3. Rate Limiting
```nginx
# Add to Nginx http block
http {
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=1r/s;
}

# Add to server block
location /api/ {
    limit_req zone=api burst=20 nodelay;
    # ... other config
}

location /api/upload {
    limit_req zone=upload burst=5 nodelay;
    # ... other config
}
```

## 📊 Monitoring & Logging

### 1. Application Monitoring
```bash
# PM2 monitoring
pm2 monit

# System monitoring
sudo apt install htop iotop -y
```

### 2. Log Management
```bash
# Logrotate configuration
sudo cat > /etc/logrotate.d/tax-ai << 'EOF'
/path/to/aplikasi-cek-pajak/backend/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload tax-ai-backend
    endscript
}
EOF
```

### 3. Health Checks
```bash
# Create health check script
cat > health-check.sh << 'EOF'
#!/bin/bash
curl -f http://localhost:5000/health || exit 1
EOF

chmod +x health-check.sh

# Add to crontab
echo "*/5 * * * * /path/to/health-check.sh" | crontab -
```

## 🔄 Backup & Recovery

### 1. Database Backup
```bash
# Create backup script
cat > backup-db.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U taxai_user taxai > backup_${DATE}.sql
aws s3 cp backup_${DATE}.sql s3://your-backup-bucket/
rm backup_${DATE}.sql
EOF

chmod +x backup-db.sh

# Schedule daily backups
echo "0 2 * * * /path/to/backup-db.sh" | crontab -
```

### 2. File Backup
```bash
# Backup uploads and configurations
tar -czf backup_files_$(date +%Y%m%d).tar.gz \
    backend/uploads \
    backend/.env \
    nginx/ssl

aws s3 cp backup_files_*.tar.gz s3://your-backup-bucket/
```

## 🚀 CI/CD Pipeline

### GitHub Actions Example
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /path/to/aplikasi-cek-pajak
            git pull origin main
            npm run build
            pm2 reload tax-ai-backend
```

## 🔧 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   sudo lsof -i :5000
   sudo kill -9 <PID>
   ```

2. **Database connection failed**
   ```bash
   sudo systemctl status postgresql
   sudo systemctl restart postgresql
   ```

3. **SSL certificate issues**
   ```bash
   sudo certbot renew --dry-run
   sudo systemctl reload nginx
   ```

4. **High memory usage**
   ```bash
   pm2 restart tax-ai-backend
   docker-compose restart
   ```

### Performance Optimization

1. **Enable Gzip compression**
2. **Setup CDN for static assets**
3. **Database query optimization**
4. **Redis caching implementation**
5. **Load balancing for high traffic**

## 📞 Support

Jika mengalami masalah deployment:
- Check logs: `docker-compose logs` atau `pm2 logs`
- Monitor resources: `htop`, `docker stats`
- Database status: `sudo systemctl status postgresql`
- Nginx status: `sudo systemctl status nginx`

---

**Happy Deploying! 🚀**
