{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/postcss": "^4.0.0", "@tailwindcss/vite": "^4.0.0", "@tanstack/react-query": "^5.85.5", "axios": "^1.11.0", "framer-motion": "^12.23.12", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-pdf": "^10.1.0", "socket.io-client": "^4.8.1", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}