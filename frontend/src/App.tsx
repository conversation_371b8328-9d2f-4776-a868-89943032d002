import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import ChatLayout from './components/ChatLayout'
import './App.css'

const queryClient = new QueryClient()

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <ChatLayout />
      </div>
    </QueryClientProvider>
  )
}

export default App
