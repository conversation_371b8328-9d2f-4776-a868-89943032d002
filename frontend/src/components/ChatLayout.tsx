import { useState } from 'react'
import { MessageSquare, FileText, Upload, Settings, Menu, X } from 'lucide-react'
import ChatInterface from './ChatInterface'
import Sidebar from './Sidebar'
import PDFViewer from './PDFViewer'

const ChatLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [currentView, setCurrentView] = useState<'chat' | 'pdf' | 'upload'>('chat')
  const [selectedPDF, setSelectedPDF] = useState<string | null>(null)

  return (
    <div className="flex h-screen bg-white dark:bg-gray-900">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 overflow-hidden`}>
        <Sidebar 
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          onViewChange={setCurrentView}
          currentView={currentView}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
              </button>
              
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                    AI Konsultan Pajak
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Powered by LeanRAG
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentView('upload')}
                className={`p-2 rounded-lg transition-colors ${
                  currentView === 'upload'
                    ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <Upload size={20} />
              </button>
              
              <button
                onClick={() => setCurrentView('pdf')}
                className={`p-2 rounded-lg transition-colors ${
                  currentView === 'pdf'
                    ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <FileText size={20} />
              </button>
              
              <button
                onClick={() => setCurrentView('chat')}
                className={`p-2 rounded-lg transition-colors ${
                  currentView === 'chat'
                    ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <MessageSquare size={20} />
              </button>

              <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <Settings size={20} />
              </button>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <div className="flex-1 flex">
          {/* Main Chat/Content */}
          <div className="flex-1">
            {currentView === 'chat' && <ChatInterface />}
            {currentView === 'pdf' && <PDFViewer pdfUrl={selectedPDF} />}
            {currentView === 'upload' && (
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">Upload Dokumen Pajak</h2>
                {/* Upload component will be implemented */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-600">Drag & drop PDF files here or click to browse</p>
                </div>
              </div>
            )}
          </div>

          {/* PDF Preview Panel (when viewing PDF) */}
          {currentView === 'chat' && selectedPDF && (
            <div className="w-96 border-l border-gray-200 dark:border-gray-700">
              <PDFViewer pdfUrl={selectedPDF} />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ChatLayout
