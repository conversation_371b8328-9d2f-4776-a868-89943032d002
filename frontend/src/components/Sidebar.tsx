import { useState } from 'react'
import { 
  MessageSquare, 
  FileText, 
  Upload, 
  History, 
  Trash2, 
  Plus,
  Search,
  Filter,
  Calendar,
  Tag
} from 'lucide-react'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
  onViewChange: (view: 'chat' | 'pdf' | 'upload') => void
  currentView: 'chat' | 'pdf' | 'upload'
}

interface ChatSession {
  id: string
  title: string
  timestamp: Date
  preview: string
  category: 'spt' | 'bukti-potong' | 'general'
}

const Sidebar = ({ isOpen, onClose, onViewChange, currentView }: SidebarProps) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  
  // Mock data - akan diganti dengan data dari API
  const [chatSessions] = useState<ChatSession[]>([
    {
      id: '1',
      title: 'SPT Tahunan 2024',
      timestamp: new Date('2024-08-29'),
      preview: 'Bagaimana cara mengisi SPT Tahunan untuk...',
      category: 'spt'
    },
    {
      id: '2',
      title: '<PERSON><PERSON><PERSON>g <PERSON>h 21',
      timestamp: new Date('2024-08-28'),
      preview: 'Saya ingin menanyakan tentang bukti potong...',
      category: 'bukti-potong'
    },
    {
      id: '3',
      title: 'Konsultasi Pajak Umum',
      timestamp: new Date('2024-08-27'),
      preview: 'Apa saja kewajiban pajak untuk freelancer...',
      category: 'general'
    }
  ])

  const categories = [
    { id: 'all', label: 'Semua', count: chatSessions.length },
    { id: 'spt', label: 'SPT', count: chatSessions.filter(s => s.category === 'spt').length },
    { id: 'bukti-potong', label: 'Bukti Potong', count: chatSessions.filter(s => s.category === 'bukti-potong').length },
    { id: 'general', label: 'Umum', count: chatSessions.filter(s => s.category === 'general').length }
  ]

  const filteredSessions = chatSessions.filter(session => {
    const matchesSearch = session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         session.preview.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || session.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'spt': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'bukti-potong': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'general': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  if (!isOpen) return null

  return (
    <div className="h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <button
          onClick={() => onViewChange('chat')}
          className="w-full flex items-center space-x-3 p-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg hover:from-green-600 hover:to-blue-600 transition-all"
        >
          <Plus size={20} />
          <span className="font-medium">Chat Baru</span>
        </button>
      </div>

      {/* Search */}
      <div className="p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <input
            type="text"
            placeholder="Cari riwayat chat..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Categories */}
      <div className="px-4 pb-4">
        <div className="flex items-center space-x-2 mb-3">
          <Filter size={16} className="text-gray-500" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Kategori</span>
        </div>
        <div className="flex flex-wrap gap-2">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              {category.label} ({category.count})
            </button>
          ))}
        </div>
      </div>

      {/* Chat History */}
      <div className="flex-1 overflow-y-auto">
        <div className="px-4 pb-2">
          <div className="flex items-center space-x-2 mb-3">
            <History size={16} className="text-gray-500" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Riwayat Chat</span>
          </div>
        </div>

        <div className="space-y-2 px-4">
          {filteredSessions.map(session => (
            <div
              key={session.id}
              className="group p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {session.title}
                    </h3>
                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(session.category)}`}>
                      <Tag size={10} className="inline mr-1" />
                      {session.category}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2 mb-2">
                    {session.preview}
                  </p>
                  <div className="flex items-center space-x-1 text-xs text-gray-400">
                    <Calendar size={10} />
                    <span>{session.timestamp.toLocaleDateString('id-ID')}</span>
                  </div>
                </div>
                <button className="opacity-0 group-hover:opacity-100 p-1 hover:bg-red-100 dark:hover:bg-red-900 rounded transition-all">
                  <Trash2 size={14} className="text-red-500" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredSessions.length === 0 && (
          <div className="px-4 py-8 text-center">
            <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              {searchQuery ? 'Tidak ada chat yang ditemukan' : 'Belum ada riwayat chat'}
            </p>
          </div>
        )}
      </div>

      {/* Navigation */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-3 gap-2">
          <button
            onClick={() => onViewChange('chat')}
            className={`p-3 rounded-lg transition-colors ${
              currentView === 'chat'
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                : 'hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
          >
            <MessageSquare size={20} className="mx-auto mb-1" />
            <span className="text-xs">Chat</span>
          </button>
          
          <button
            onClick={() => onViewChange('pdf')}
            className={`p-3 rounded-lg transition-colors ${
              currentView === 'pdf'
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                : 'hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
          >
            <FileText size={20} className="mx-auto mb-1" />
            <span className="text-xs">PDF</span>
          </button>
          
          <button
            onClick={() => onViewChange('upload')}
            className={`p-3 rounded-lg transition-colors ${
              currentView === 'upload'
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                : 'hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
          >
            <Upload size={20} className="mx-auto mb-1" />
            <span className="text-xs">Upload</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default Sidebar
