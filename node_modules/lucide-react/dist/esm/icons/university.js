/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M14 21v-3a2 2 0 0 0-4 0v3", key: "1rgiei" }],
  ["path", { d: "M18 12h.01", key: "yjnet6" }],
  ["path", { d: "M18 16h.01", key: "plv8zi" }],
  [
    "path",
    {
      d: "M22 7a1 1 0 0 0-1-1h-2a2 2 0 0 1-1.143-.359L13.143 2.36a2 2 0 0 0-2.286-.001L6.143 5.64A2 2 0 0 1 5 6H3a1 1 0 0 0-1 1v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2z",
      key: "1ogmi3"
    }
  ],
  ["path", { d: "M6 12h.01", key: "c2rlol" }],
  ["path", { d: "M6 16h.01", key: "1pmjb7" }],
  ["circle", { cx: "12", cy: "10", r: "2", key: "1yojzk" }]
];
const University = createLucideIcon("university", __iconNode);

export { __iconNode, University as default };
//# sourceMappingURL=university.js.map
