#!/bin/bash

# 🏛️ Aplikasi AI Konsultan <PERSON> - Setup Script
# Automated setup untuk development environment

set -e  # Exit on any error

echo "🚀 Starting setup for Aplikasi AI Konsultan <PERSON>jak..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js not found. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_success "npm found: $NPM_VERSION"
    else
        print_error "npm not found. Please install npm"
        exit 1
    fi
    
    # Check Python
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version)
        print_success "Python found: $PYTHON_VERSION"
    else
        print_error "Python 3 not found. Please install Python 3.10+ from https://python.org/"
        exit 1
    fi
    
    # Check pip
    if command_exists pip3; then
        PIP_VERSION=$(pip3 --version)
        print_success "pip found: $PIP_VERSION"
    else
        print_error "pip3 not found. Please install pip3"
        exit 1
    fi
    
    # Check Git
    if command_exists git; then
        GIT_VERSION=$(git --version)
        print_success "Git found: $GIT_VERSION"
    else
        print_error "Git not found. Please install Git"
        exit 1
    fi
}

# Install Node.js dependencies
install_node_dependencies() {
    print_status "Installing Node.js dependencies..."
    
    # Root dependencies
    print_status "Installing root dependencies..."
    npm install
    
    # Frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    
    # Backend dependencies
    print_status "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    
    print_success "Node.js dependencies installed successfully"
}

# Install Python dependencies
install_python_dependencies() {
    print_status "Installing Python dependencies for LeanRAG..."
    
    cd LeanRAG
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    print_status "Activating virtual environment..."
    source venv/bin/activate
    
    # Upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip
    
    # Install requirements
    print_status "Installing Python requirements..."
    pip install -r requirements.txt
    
    cd ..
    
    print_success "Python dependencies installed successfully"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        print_status "Creating backend .env file..."
        cp backend/.env.example backend/.env
        print_warning "Please edit backend/.env with your configuration"
    else
        print_warning "backend/.env already exists, skipping..."
    fi
    
    # Create necessary directories
    print_status "Creating necessary directories..."
    mkdir -p backend/uploads
    mkdir -p backend/uploads/downloads
    mkdir -p backend/logs
    mkdir -p LeanRAG/output
    mkdir -p LeanRAG/chunks
    mkdir -p LeanRAG/graphs
    
    print_success "Environment setup completed"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    cd backend
    
    # Generate Prisma client (if using Prisma)
    if [ -f "prisma/schema.prisma" ]; then
        print_status "Generating Prisma client..."
        npx prisma generate
        
        print_status "Running database migrations..."
        npx prisma db push
    fi
    
    cd ..
    
    print_success "Database setup completed"
}

# Build frontend
build_frontend() {
    print_status "Building frontend for development..."
    
    cd frontend
    npm run build
    cd ..
    
    print_success "Frontend build completed"
}

# Test installations
test_installations() {
    print_status "Testing installations..."
    
    # Test frontend
    print_status "Testing frontend..."
    cd frontend
    npm run lint --silent || print_warning "Frontend linting issues found"
    cd ..
    
    # Test backend
    print_status "Testing backend..."
    cd backend
    npm run lint --silent || print_warning "Backend linting issues found"
    cd ..
    
    print_success "Installation tests completed"
}

# Create startup scripts
create_startup_scripts() {
    print_status "Creating startup scripts..."
    
    # Development startup script
    cat > start-dev.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Aplikasi AI Konsultan Pajak in development mode..."

# Start LeanRAG virtual environment
cd LeanRAG && source venv/bin/activate && cd ..

# Start frontend and backend concurrently
npm run dev
EOF
    
    chmod +x start-dev.sh
    
    # Production startup script
    cat > start-prod.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Aplikasi AI Konsultan Pajak in production mode..."

# Build frontend
npm run build

# Start backend
npm start
EOF
    
    chmod +x start-prod.sh
    
    print_success "Startup scripts created"
}

# Main setup function
main() {
    echo "🏛️ Aplikasi AI Konsultan Pajak Setup"
    echo "======================================"
    
    check_prerequisites
    install_node_dependencies
    install_python_dependencies
    setup_environment
    setup_database
    test_installations
    create_startup_scripts
    
    echo ""
    echo "======================================"
    print_success "Setup completed successfully! 🎉"
    echo ""
    echo "Next steps:"
    echo "1. Edit backend/.env with your configuration"
    echo "2. Start development server: ./start-dev.sh or npm run dev"
    echo "3. Open http://localhost:3000 in your browser"
    echo ""
    echo "For production:"
    echo "1. Run: ./start-prod.sh"
    echo ""
    echo "Documentation: README.md"
    echo "Support: https://github.com/your-repo/issues"
    echo "======================================"
}

# Run main function
main "$@"
